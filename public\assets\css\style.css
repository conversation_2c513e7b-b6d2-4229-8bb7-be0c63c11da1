@import url('https://fonts.googleapis.com/css2?family=Archivo+Black&family=Inter:wght@400;500;600;700;800&display=swap');

body {
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  /* Mengatasi horizontal scroll saat sidebar dianimasikan */
}

.font-archivoblack {
  font-family: 'Archivo Black', sans-serif;
}

.sidebar {
  width: 260px;
  min-width: 260px;
  height: 100vh;
  background-color: #1E40AF;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  overflow-y: auto;
  transition: all 0.3s ease;
  z-index: 10;
}

.sidebar-minimized {
  width: 80px;
  min-width: 80px;
}

.main-content {
  margin-left: 260px;
  background-color: #F5F9FF;
  min-height: 100vh;
  transition: margin-left 0.3s ease;
}

.main-content-minimized {
  margin-left: 80px;
}

.sidebar-link-icon-container {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: #DBEAFE;
  color: #3B82F6;
  margin-right: 12px;
}

.sidebar-minimized .sidebar-link-icon-container {
  margin-right: 0;
}

.sidebar-link-text {
  opacity: 1;
  transition: opacity 0.1s ease;
  white-space: nowrap;
}

.sidebar-minimized .sidebar-link-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 400px;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 50;
}

.chart-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: inline-block;
  background-image: conic-gradient(#3B82F6 0% 70%, #E5E7EB 70% 100%);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-circle::after {
  content: '';
  position: absolute;
  background: white;
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.chart-text {
  position: relative;
  z-index: 10;
  font-size: 1.5rem;
  font-weight: bold;
  color: #1E40AF;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Style for train timeline and table */
.timeline-section {
  background: #FFFFFF;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}

.timeline {
  height: 170px;
  background: #E0E7FF;
  border-radius: 10px;
  position: relative;
  overflow-x: auto;
  padding: 20px 0;
}

.timeline-inner {
  min-width: 1440px;
  height: 100%;
  position: relative;
}

.timeline-hours {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30px;
}

.timeline-hour {
  flex: 1;
  text-align: center;
  border-right: 1px solid #C0C0C0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  position: relative;
  color: #6B7280;
}

.timeline-minutes {
  position: absolute;
  top: 35px;
  left: 0;
  right: 0;
  height: 15px;
  display: flex;
}

.timeline-minute {
  flex: 1;
  border-right: 1px solid #D1D5DB;
  position: relative;
}

.timeline-now {
  position: absolute;
  top: 50px;
  bottom: 0;
  width: 3px;
  background: #8B5CF6;
  z-index: 10;
  left: 0;
  box-shadow: 0 0 10px #8B5CF6;
}

.timeline-now::before {
  content: attr(data-current-time);
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  font-weight: bold;
  color: #8B5CF6;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  background: white;
  padding: 3px 8px;
  border-radius: 4px;
}

.timeline-trains {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
}

.timeline-train {
  position: absolute;
  height: 40px;
  background: #3B82F6;
  color: white;
  border-radius: 5px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 5;
}

.timeline-train:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.timeline-train.arrived {
  background: #10B981;
}

.timeline-train.departed {
  background: #6B7280;
}

.timeline-train .train-number {
  font-weight: bold;
  margin-right: 5px;
  font-size: 14px;
}

.timeline-train .train-name {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.timeline-train .train-time {
  margin-left: auto;
  font-size: 12px;
  font-weight: bold;
}

.status-arrived {
  color: #10B981;
  font-weight: bold;
}

.status-departed {
  color: #6B7280;
  font-weight: bold;
}

.status-scheduled {
  color: #3B82F6;
  font-weight: bold;
}

.table-section, .emplasemen-section, .jalur-info-section {
  background: #FFFFFF;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.search-box {
  position: relative;
  max-width: 300px;
  width: 100%;
}

.search-box input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border-radius: 50px;
  border: 1px solid #E5E7EB;
  background: #F9FAFB;
  color: #111827;
  outline: none;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
}

.search-box input::placeholder {
  color: #9CA3AF;
}

.train-table-container, .jalur-table-container, .ibpr-table-container {
  overflow-x: auto;
}

.train-table, .jalur-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}

.ibpr-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 12px;
  /* Compact font size */
  table-layout: fixed;
  /* Mencegah tabel melebar saat edit */
}

.train-table th, .jalur-table th {
  padding: 10px 8px;
  font-weight: 600;
  border-bottom: 2px solid #E5E7EB;
  color: #6B7280;
  white-space: normal;
  text-align: center;
  vertical-align: middle;
}

.ibpr-table th {
  padding: 6px 8px;
  /* Reduced padding */
  font-weight: 600;
  border-bottom: 2px solid #E5E7EB;
  color: #6B7280;
  white-space: normal;
  /* Allow text wrapping */
  text-align: center;
  vertical-align: middle;
}

.bg-blue-600 th {
  color: white;
}

.train-table td, .jalur-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #E5E7EB;
  color: #374151;
  white-space: normal;
  text-align: center;
  vertical-align: top;
  word-wrap: break-word;
}

.ibpr-table td {
  padding: 6px 8px;
  /* Reduced padding */
  border-bottom: 1px solid #E5E7EB;
  color: #374151;
  white-space: normal;
  /* Allow text wrapping */
  text-align: center;
  vertical-align: top;
  /* Align content to top */
  word-wrap: break-word;
  /* Memastikan teks panjang tidak memotong layout */
}

.train-table tbody tr:hover, .jalur-table tbody tr:hover, .ibpr-table tbody tr:hover {
  background-color: #F3F4F6;
}

.ibpr-table td input, .ibpr-table td select, .ibpr-table td textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 4px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
}

.ibpr-table td textarea {
  min-height: 40px;
  /* Reduced height */
  white-space: pre-wrap;
}

.ibpr-table .text-left {
  text-align: left;
}

.title-divider {
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #3B82F6;
  width: 100%;
}

.editable-table td input, .editable-table td select {
  width: 100%;
  box-sizing: border-box;
  padding: 4px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.rotate-180 {
  transform: rotate(180deg);
}

.sidebar-minimized #administrasi-submenu {
  display: none !important;
}

.sidebar-minimized #menu-administrasi-toggle .fa-chevron-down {
  display: none;
}

/* Animasi untuk submenu */
.submenu-transition {
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.submenu-open {
  max-height: 500px;
  /* Nilai yang cukup besar untuk menampung semua item */
  opacity: 1;
}